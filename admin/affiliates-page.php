<?php
if (!defined('ABSPATH')) exit; // Bloqueia acesso direto

function e1copy_ai_affiliates_page() {
    // Verificar mensagens
    if (isset($_GET['message'])) {
        $messages = [
            'created' => 'Afiliado criado com sucesso!',
            'updated' => 'Afiliado atualizado com sucesso!',
            'deleted' => 'Afiliado excluído com sucesso!'
        ];

        if (isset($messages[$_GET['message']])) {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html($messages[$_GET['message']]) . '</p></div>';
        }
    }

    // Verificar se estamos no modo de formulário
    if (isset($_GET['action']) && $_GET['action'] === 'new') {
        e1copy_ai_display_affiliate_form(0);
        return;
    }

    // Verificar se estamos no modo de edição
    if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['affiliate_id'])) {
        $affiliate_id = intval($_GET['affiliate_id']);
        e1copy_ai_display_affiliate_form($affiliate_id);
        return;
    }

    // Processar exclusão de afiliado
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['affiliate_id']) && isset($_GET['_wpnonce'])) {
        $affiliate_id = intval($_GET['affiliate_id']);

        // Verificar nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'delete_affiliate_' . $affiliate_id)) {
            wp_die('Ação não autorizada.');
        }

        // Excluir o afiliado
        $result = e1copy_ai_delete_affiliate($affiliate_id);

        if ($result) {
            wp_redirect(admin_url('admin.php?page=e1copy-ai-affiliates&message=deleted'));
            exit;
        } else {
            wp_die('Erro ao remover o afiliado.');
        }
    }

    // Processar salvamento de afiliado
    if (isset($_POST['action']) && $_POST['action'] === 'save_affiliate') {
        // Verificar nonce
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'e1copy_save_affiliate')) {
            wp_die('Ação não autorizada.');
        }

        // Preparar dados
        $affiliate_data = [
            'title' => sanitize_text_field($_POST['affiliate_title']),
            'affiliate_link' => isset($_POST['affiliate_link']) ? esc_url_raw($_POST['affiliate_link']) : '',
            'card_text' => isset($_POST['card_text']) ? sanitize_textarea_field($_POST['card_text']) : '',
            'button_text' => isset($_POST['button_text']) ? sanitize_text_field($_POST['button_text']) : 'Ver Produto',
            'product_image' => isset($_POST['product_image']) ? esc_url_raw($_POST['product_image']) : 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080',
            'template_id' => isset($_POST['template_id']) ? sanitize_text_field($_POST['template_id']) : 'model1',
            'content' => isset($_POST['content']) ? $_POST['content'] : '',
            'custom_css' => isset($_POST['custom_css']) ? $_POST['custom_css'] : '',
        ];

        // Verificar se é uma atualização ou novo afiliado
        if (isset($_POST['affiliate_id']) && intval($_POST['affiliate_id']) > 0) {
            $affiliate_data['id'] = intval($_POST['affiliate_id']);
        }

        // Verificar se a tabela tem as colunas necessárias
        global $wpdb;
        $table_name = $wpdb->prefix . 'e1copy_affiliates';
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        $column_names = [];
        foreach ($columns as $column) {
            $column_names[] = $column->Field;
        }

        // Se a coluna affiliate_link não existir, adicioná-la
        if (!in_array('affiliate_link', $column_names)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN affiliate_link text DEFAULT ''");
            error_log('E1Copy Handler: Coluna affiliate_link adicionada à tabela de afiliados durante o salvamento');
        }

        // Se a coluna custom_css não existir, adicioná-la
        if (!in_array('custom_css', $column_names)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN custom_css text DEFAULT ''");
            error_log('E1Copy Handler: Coluna custom_css adicionada à tabela de afiliados durante o salvamento');
        }

        // Se a coluna card_text não existir, adicioná-la
        if (!in_array('card_text', $column_names)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN card_text text DEFAULT ''");
            error_log('E1Copy Handler: Coluna card_text adicionada à tabela de afiliados durante o salvamento');
        }

        // Se a coluna button_text não existir, adicioná-la
        if (!in_array('button_text', $column_names)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN button_text varchar(255) DEFAULT 'Ver Produto'");
            error_log('E1Copy Handler: Coluna button_text adicionada à tabela de afiliados durante o salvamento');
        }

        // Se a coluna product_image não existir, adicioná-la
        if (!in_array('product_image', $column_names)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN product_image text DEFAULT ''");
            error_log('E1Copy Handler: Coluna product_image adicionada à tabela de afiliados durante o salvamento');
        }

        // Se a coluna template_id não existir, adicioná-la
        if (!in_array('template_id', $column_names)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN template_id varchar(50) DEFAULT 'model1'");
            error_log('E1Copy Handler: Coluna template_id adicionada à tabela de afiliados durante o salvamento');
        }

        // Verificar se o campo custom_css está presente
        if (!isset($affiliate_data['custom_css']) && isset($_POST['custom_css'])) {
            $affiliate_data['custom_css'] = sanitize_textarea_field($_POST['custom_css']);
            error_log('E1Copy Handler: Campo custom_css adicionado manualmente: ' . substr($affiliate_data['custom_css'], 0, 50) . '...');
        }

        // Salvar os dados
        $affiliate_id = e1copy_ai_save_affiliate($affiliate_data);

        if ($affiliate_id) {
            $message = isset($_POST['affiliate_id']) ? 'updated' : 'created';
            wp_redirect(admin_url("admin.php?page=e1copy-ai-affiliates&message={$message}"));
            exit;
        } else {
            global $wpdb;
            $error_message = 'Erro ao salvar o afiliado.';

            if (!empty($wpdb->last_error)) {
                $error_message .= '<br><br>Erro no banco de dados do WordPress: [' . $wpdb->last_error . ']';
                $error_message .= '<br><br>Última consulta SQL: ' . $wpdb->last_query;
            }

            // Forçar a atualização da tabela
            e1copy_ai_create_affiliates_table();

            $error_message .= '<br><br>A estrutura da tabela foi atualizada. Por favor, tente novamente.';

            wp_die($error_message);
        }
    }

    // Exibir a listagem de afiliados
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline">Meus Afiliados</h1>
        <a href="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates&action=new'); ?>" class="page-title-action">+ Novo Afiliado</a>
        <hr class="wp-header-end">

        <!-- Os recursos CSS são carregados via wp_enqueue_style -->

        <div class="notice notice-info">
            <p>Utilize os shortcodes abaixo para inserir os afiliados em seus posts ou páginas.</p>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Título</th>
                    <th>Link Afiliado</th>
                    <th>Shortcode</th>
                    <th>Data de Criação</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $affiliates = e1copy_ai_get_all_affiliates();
                if (empty($affiliates)) {
                    echo '<tr><td colspan="4">Nenhum afiliado encontrado.</td></tr>';
                } else {
                    foreach ($affiliates as $affiliate) {
                        ?>
                        <tr>
                            <td>
                                <strong><a href="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates&action=edit&affiliate_id=' . $affiliate['id']); ?>"><?php echo esc_html($affiliate['title']); ?></a></strong>
                                <div class="row-actions">
                                    <span class="edit"><a href="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates&action=edit&affiliate_id=' . $affiliate['id']); ?>">Editar</a> | </span>
                                    <span class="trash"><a href="<?php echo wp_nonce_url(admin_url('admin.php?page=e1copy-ai-affiliates&action=delete&affiliate_id=' . $affiliate['id']), 'delete_affiliate_' . $affiliate['id']); ?>" class="submitdelete" onclick="return confirm('Tem certeza que deseja excluir este afiliado?')">Excluir</a></span>
                                </div>
                            </td>
                            <td>
                                <?php if (!empty($affiliate['affiliate_link'])): ?>
                                    <a href="<?php echo esc_url($affiliate['affiliate_link']); ?>" target="_blank"><?php echo esc_url($affiliate['affiliate_link']); ?></a>
                                <?php else: ?>
                                    <em>—</em>
                                <?php endif; ?>
                            </td>
                            <td>
                                <code>[e1copy_affiliate id="<?php echo $affiliate['id']; ?>"]</code>
                                <button type="button" class="button button-small copy-shortcode" data-shortcode='[e1copy_affiliate id="<?php echo $affiliate['id']; ?>"]'>Copiar</button>
                                <div style="margin-top: 5px;">
                                    <small>Insere o conteúdo HTML completo do afiliado</small>
                                </div>
                            </td>
                            <td>
                                <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($affiliate['created_at'])); ?>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates&action=edit&affiliate_id=' . $affiliate['id']); ?>" class="button">Editar</a>
                            </td>
                        </tr>
                        <?php
                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <script>
        jQuery(document).ready(function($) {
            $('.copy-shortcode').click(function() {
                var shortcode = $(this).data('shortcode');
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(shortcode).select();
                document.execCommand('copy');
                tempInput.remove();

                // Feedback visual
                var originalText = $(this).text();
                var $this = $(this);
                $(this).text('Copiado!');
                setTimeout(function() {
                    $this.text(originalText);
                }, 2000);
            });
        });
    </script>

    <!-- Os scripts JavaScript são carregados via wp_enqueue_script -->
    <?php
}

/**
 * Exibe o formulário para criar/editar afiliado
 */
function e1copy_ai_display_affiliate_form($affiliate_id = 0) {
    // Enfileirar scripts da biblioteca de mídia do WordPress
    wp_enqueue_media();

    // Enfileirar os ícones do WordPress
    wp_enqueue_style('dashicons');
    $affiliate_data = [
        'title' => '',
        'affiliate_link' => '',
        'card_text' => '',
        'button_text' => 'Ver Produto',
        'product_image' => 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080',
        'template_id' => 'model1',
        'content' => '',
        'custom_css' => '',
        'shortcode' => '',
    ];

    // Se for edição, carregar dados existentes
    if ($affiliate_id > 0) {
        $saved_affiliate = e1copy_ai_get_affiliate($affiliate_id);
        if (!$saved_affiliate) {
            wp_die('Afiliado não encontrado.');
        }

        $affiliate_data = array_merge($affiliate_data, $saved_affiliate);
    }

    $form_title = $affiliate_id ? 'Editar Afiliado' : 'Novo Afiliado';

    ?>
    <div class="wrap">
        <h1><?php echo $form_title; ?></h1>
        <p class="description">Crie ou edite um afiliado para usar em seus posts e páginas.</p>

        <!-- Os recursos CSS são carregados via wp_enqueue_style -->

        <form method="post" action="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates'); ?>">
            <?php wp_nonce_field('e1copy_save_affiliate'); ?>
            <input type="hidden" name="action" value="save_affiliate">
            <?php if ($affiliate_id): ?>
                <input type="hidden" name="affiliate_id" value="<?php echo $affiliate_id; ?>">
            <?php endif; ?>

            <table class="form-table">
                <tr>
                    <th scope="row"><label for="affiliate_title">Título</label> <span style="color: #dc3232; font-weight: bold;">*</span></th>
                    <td>
                        <input type="text" name="affiliate_title" id="affiliate_title" value="<?php echo esc_attr($affiliate_data['title']); ?>" class="regular-text" required>
                        <p class="description">Nome para identificação interna (não será exibido no site)</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="affiliate_link">Link Afiliado</label></th>
                    <td>
                        <input type="url" name="affiliate_link" id="affiliate_link" value="<?php echo esc_url($affiliate_data['affiliate_link']); ?>" class="regular-text">
                        <p class="description">URL principal de afiliado (pode ser usado no conteúdo ou como link padrão)</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><label for="product_image">Imagem do Produto</label></th>
                    <td>
                        <div class="product-image-container">
                            <input type="hidden" name="product_image" id="product_image" value="<?php echo esc_url(isset($affiliate_data['product_image']) && !empty($affiliate_data['product_image']) ? $affiliate_data['product_image'] : 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080'); ?>">

                            <div class="image-preview-wrapper" style="margin-bottom: 10px; max-width: 150px;">
                                <img id="product_image_preview" src="<?php echo esc_url(isset($affiliate_data['product_image']) && !empty($affiliate_data['product_image']) ? $affiliate_data['product_image'] : 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080'); ?>" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; padding: 3px;">
                            </div>

                            <div class="buttons-container">
                                <button type="button" class="button upload_image_button" id="upload_product_image">Escolher Imagem</button>
                                <button type="button" class="button remove_image_button" id="remove_product_image" style="margin-left: 10px;">Remover Imagem</button>
                            </div>

                            <p class="description" style="margin-top: 10px;">Escolha uma imagem para o produto. Se não for selecionada, será usada a imagem padrão.</p>
                        </div>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><label for="template_id">Modelo de Template</label></th>
                    <td>
                        <input type="hidden" name="template_id" id="template_id" value="<?php echo esc_attr(isset($affiliate_data['template_id']) ? $affiliate_data['template_id'] : 'model1'); ?>">
                        <button type="button" id="select_template_btn" class="button">Selecionar Modelo</button>
                        <span id="selected_template_name" style="margin-left: 10px; font-weight: bold;">
                            <?php
                            $templates = e1copy_ai_get_templates();
                            $template_id = isset($affiliate_data['template_id']) ? $affiliate_data['template_id'] : 'model1';
                            echo isset($templates[$template_id]) ? esc_html($templates[$template_id]['name']) : 'Modelo Padrão';
                            ?>
                        </span>
                        <p class="description">Selecione um modelo de template para seu afiliado</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><label for="card_text">Texto do Card</label></th>
                    <td>
                        <textarea name="card_text" id="card_text" rows="3" class="large-text"><?php echo esc_textarea(isset($affiliate_data['card_text']) ? $affiliate_data['card_text'] : ''); ?></textarea>
                        <p class="description">Texto descritivo que será exibido no card do afiliado</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><label for="button_text">Texto do Botão</label></th>
                    <td>
                        <input type="text" name="button_text" id="button_text" value="<?php echo esc_attr(isset($affiliate_data['button_text']) ? $affiliate_data['button_text'] : 'Ver Produto'); ?>" class="regular-text">
                        <p class="description">Texto que será exibido no botão de ação</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">Preview</th>
                    <td>
                        <div id="template_preview" style="margin-bottom: 15px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; border-radius: 4px;">
                            <div id="preview_content">
                                <!-- O preview será carregado aqui via JavaScript -->
                                <p class="description">Clique em "Atualizar Preview" para visualizar o template com seus dados.</p>
                            </div>
                        </div>
                        <button type="button" id="update_preview_btn" class="button button-primary">
                            <i class="dashicons dashicons-visibility" style="margin-top: 3px; margin-right: 5px;"></i> Atualizar Preview
                        </button>
                        <p class="description" style="margin-top: 10px;">O preview mostra como o afiliado será exibido no site.</p>
                    </td>
                </tr>

                <!-- Campos ocultos para o conteúdo gerado -->
                <input type="hidden" name="content" id="affiliate_content" value="<?php echo esc_attr(isset($affiliate_data['content']) ? $affiliate_data['content'] : ''); ?>">
                <input type="hidden" name="custom_css" id="custom_css" value="<?php echo esc_attr(isset($affiliate_data['custom_css']) ? $affiliate_data['custom_css'] : ''); ?>">
                <?php if ($affiliate_id && !empty($affiliate_data['shortcode'])): ?>
                <tr>
                    <th scope="row">Shortcode</th>
                    <td>
                        <p>
                            <code>[e1copy_affiliate id="<?php echo $affiliate_id; ?>"]</code>
                            <button type="button" class="button copy-shortcode" data-shortcode='[e1copy_affiliate id="<?php echo $affiliate_id; ?>"]'>Copiar Shortcode</button>
                            <span class="description" style="display:block;margin-top:5px;">Insere o conteúdo HTML completo do afiliado</span>
                        </p>
                    </td>
                </tr>
                <?php endif; ?>
            </table>

            <p class="submit">
                <button type="submit" class="button button-primary">Salvar Afiliado</button>
                <a href="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates'); ?>" class="button">Cancelar</a>
            </p>
        </form>
    </div>

    <script>
        jQuery(document).ready(function($) {
            // Variáveis para armazenar o template selecionado
            var selectedTemplateId = $('#template_id').val() || 'model1';
            var templates = <?php echo json_encode(e1copy_ai_get_templates()); ?>;

            // Função para atualizar o preview
            function updatePreview() {
                var title = $('#affiliate_title').val();
                var affiliateLink = $('#affiliate_link').val();
                var cardText = $('#card_text').val();
                var buttonText = $('#button_text').val() || 'Ver Produto';
                var productImage = $('#product_image').val();

                // Obter o HTML do template selecionado
                var templateHtml = templates[selectedTemplateId].html;

                // Substituir os placeholders pelos valores do formulário
                templateHtml = templateHtml
                    .replace(/{{Título_definido_no_formulario}}/g, title)
                    .replace(/{{Link_Afiliado}}/g, affiliateLink)
                    .replace(/{{Texto_do_card}}/g, cardText)
                    .replace(/{{Texto_do_botao}}/g, buttonText)
                    .replace(/{{Imagem_do_Produto}}/g, productImage);

                // Limpar o conteúdo atual
                $('#preview_content').empty();

                // Criar um contêiner com estilos isolados para o preview
                var previewContainer = $('<div class="e1copy-preview-container"></div>');
                previewContainer.css({
                    'display': 'flex',
                    'justify-content': 'center',
                    'align-items': 'flex-start',
                    'padding': '20px',
                    'background-color': '#f9f9f9',
                    'border-radius': '4px',
                    'min-height': '200px'
                });

                // Adicionar o HTML do template ao contêiner
                previewContainer.html(templateHtml);

                // Adicionar o contêiner ao elemento de preview
                $('#preview_content').append(previewContainer);

                // Carregar os recursos CSS necessários se ainda não estiverem carregados
                if (!$('#e1copy-preview-resources').length) {
                    var resourcesContainer = $('<div id="e1copy-preview-resources"></div>');
                    resourcesContainer.html(
                        '<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">' +
                        '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">' +
                        '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">' +
                        '<style>' +
                        '.e1copy-preview-container .card { box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 0 auto; }' +
                        '.e1copy-preview-container .card-img-top { height: 200px; object-fit: cover; }' +
                        '.e1copy-preview-container .card-body { padding: 1.25rem; }' +
                        '.e1copy-preview-container .card-footer { padding: 0.75rem 1.25rem; }' +
                        '.e1copy-preview-container .btn-primary { background-color: #0073aa; border-color: #0073aa; }' +
                        '.e1copy-preview-container .btn-primary:hover { background-color: #005177; border-color: #005177; }' +
                        '</style>'
                    );
                    $('head').append(resourcesContainer);
                }

                // Atualizar o campo oculto com o HTML gerado
                $('#affiliate_content').val(templateHtml);
            }

            // Botão para atualizar o preview
            $('#update_preview_btn').click(function(e) {
                e.preventDefault();
                updatePreview();
            });

            // Abrir o modal de seleção de templates
            $('#select_template_btn').click(function(e) {
                e.preventDefault();
                $('#template_selection_modal').show();

                // Destacar o template selecionado
                $('.template-item').removeClass('selected').css('background-color', '');
                $('.template-item[data-template-id="' + selectedTemplateId + '"]').addClass('selected').css('background-color', '#e7f1ff');
            });

            // Fechar o modal
            $('#close_modal_btn, #cancel_template_selection_btn').click(function() {
                $('#template_selection_modal').hide();
            });

            // Selecionar um template no modal
            $('.template-item').click(function() {
                $('.template-item').removeClass('selected').css('background-color', '');
                $(this).addClass('selected').css('background-color', '#e7f1ff');
                selectedTemplateId = $(this).data('template-id');
            });

            // Confirmar a seleção do template
            $('#confirm_template_selection_btn').click(function() {
                // Atualizar o campo oculto com o ID do template selecionado
                $('#template_id').val(selectedTemplateId);

                // Atualizar o nome do template exibido
                $('#selected_template_name').text(templates[selectedTemplateId].name);

                // Fechar o modal
                $('#template_selection_modal').hide();

                // Atualizar o preview
                updatePreview();
            });

            // Manipulação de upload de imagem
            var file_frame;

            // Upload de imagem do produto
            $('#upload_product_image').on('click', function(event) {
                event.preventDefault();

                // Se o frame de mídia já existe, reabra-o
                if (file_frame) {
                    file_frame.open();
                    return;
                }

                // Criar o frame de mídia
                file_frame = wp.media.frames.file_frame = wp.media({
                    title: 'Selecionar ou Fazer Upload de uma Imagem',
                    button: {
                        text: 'Usar esta imagem'
                    },
                    multiple: false
                });

                // Quando uma imagem é selecionada
                file_frame.on('select', function() {
                    var attachment = file_frame.state().get('selection').first().toJSON();
                    $('#product_image').val(attachment.url);
                    $('#product_image_preview').attr('src', attachment.url);
                    updatePreview();
                });

                // Abrir o frame de mídia
                file_frame.open();
            });

            // Remover imagem do produto
            $('#remove_product_image').on('click', function(event) {
                event.preventDefault();
                var defaultImage = 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080';
                $('#product_image').val(defaultImage);
                $('#product_image_preview').attr('src', defaultImage);
                updatePreview();
            });

            // Atualizar o preview ao carregar a página
            updatePreview();

            // Atualizar o preview quando os campos do formulário forem alterados
            $('#affiliate_title, #affiliate_link, #card_text, #button_text, #product_image').on('change keyup', function() {
                updatePreview();
            });

            // Copiar shortcode
            $('.copy-shortcode').click(function() {
                var shortcode = $(this).data('shortcode');
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(shortcode).select();
                document.execCommand('copy');
                tempInput.remove();

                // Feedback visual
                var originalText = $(this).text();
                $(this).text('Copiado!');
                var $this = $(this);
                setTimeout(function() {
                    $this.text(originalText);
                }, 2000);
            });
        });
    </script>

    <!-- Os scripts JavaScript são carregados via wp_enqueue_script -->

    <!-- Modal de seleção de templates -->
    <div id="template_selection_modal" style="display: none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
        <div style="background-color: #fefefe; margin: 5% auto; padding: 20px; border: 1px solid #888; width: 80%; max-width: 800px; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0;">Selecione um Modelo de Template</h2>
                <button type="button" id="close_modal_btn" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>

            <div id="templates_container" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px;">
                <?php
                $templates = e1copy_ai_get_templates();
                foreach ($templates as $id => $template) {
                    ?>
                    <div class="template-item" data-template-id="<?php echo esc_attr($id); ?>" style="border: 1px solid #ddd; border-radius: 5px; padding: 15px; cursor: pointer; transition: all 0.2s ease;">
                        <h3><?php echo esc_html($template['name']); ?></h3>
                        <p><?php echo esc_html($template['description']); ?></p>
                        <div style="margin-top: 10px; border: 1px solid #eee; padding: 10px; background: #f9f9f9; border-radius: 4px; max-height: 200px; overflow: auto;">
                            <pre style="margin: 0; white-space: pre-wrap; font-size: 12px;"><?php echo esc_html($template['html']); ?></pre>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>

            <div style="margin-top: 20px; text-align: right;">
                <button type="button" id="cancel_template_selection_btn" class="button">Cancelar</button>
                <button type="button" id="confirm_template_selection_btn" class="button button-primary" style="margin-left: 10px;">Selecionar</button>
            </div>
        </div>
    </div>
    <?php
}
