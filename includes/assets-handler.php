<?php
/**
 * Gerencia os assets (CSS e JavaScript) do plugin
 */

if (!defined('ABSPATH')) exit; // Bloqueia acesso direto

/**
 * Registra e enfileira os estilos e scripts necessários
 */
function e1copy_ai_register_assets() {
    // Registrar os estilos e scripts, mas não enfileirá-los globalmente
    // Eles serão enfileirados apenas quando necessário

    // Registrar o Bootstrap 4.4.1 do CDN (versão específica solicitada)
    wp_register_style(
        'e1copy-bootstrap',
        'https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css',
        [],
        '4.4.1',
        'all'
    );

    // Registrar os ícones do Bootstrap
    wp_register_style(
        'e1copy-bootstrap-icons',
        'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css',
        [],
        '1.7.2',
        'all'
    );

    // Registrar o Font Awesome
    wp_register_style(
        'e1copy-font-awesome',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css',
        [],
        '6.4.2',
        'all'
    );

    // Registrar o JavaScript do Bootstrap (com dependência do jQuery)
    wp_register_script(
        'e1copy-bootstrap-js',
        'https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js',
        ['jquery'],
        '4.4.1',
        true
    );

    // Registrar o Popper.js (necessário para Bootstrap 4)
    wp_register_script(
        'e1copy-popper-js',
        'https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js',
        ['jquery'],
        '1.16.0',
        true
    );

    // Adicionar CSS personalizado para evitar conflitos com o tema
    wp_register_style('e1copy-affiliate-custom-css', false);
    wp_add_inline_style('e1copy-affiliate-custom-css', '
        /* Estilos para evitar conflitos com o tema */
        .e1copy-affiliate .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }
        .e1copy-affiliate .col,
        .e1copy-affiliate .col-1, .e1copy-affiliate .col-2, .e1copy-affiliate .col-3,
        .e1copy-affiliate .col-4, .e1copy-affiliate .col-5, .e1copy-affiliate .col-6,
        .e1copy-affiliate .col-7, .e1copy-affiliate .col-8, .e1copy-affiliate .col-9,
        .e1copy-affiliate .col-10, .e1copy-affiliate .col-11, .e1copy-affiliate .col-12,
        .e1copy-affiliate .col-lg-1, .e1copy-affiliate .col-lg-2, .e1copy-affiliate .col-lg-3,
        .e1copy-affiliate .col-lg-4, .e1copy-affiliate .col-lg-5, .e1copy-affiliate .col-lg-6,
        .e1copy-affiliate .col-lg-7, .e1copy-affiliate .col-lg-8, .e1copy-affiliate .col-lg-9,
        .e1copy-affiliate .col-lg-10, .e1copy-affiliate .col-lg-11, .e1copy-affiliate .col-lg-12,
        .e1copy-affiliate .col-md-1, .e1copy-affiliate .col-md-2, .e1copy-affiliate .col-md-3,
        .e1copy-affiliate .col-md-4, .e1copy-affiliate .col-md-5, .e1copy-affiliate .col-md-6,
        .e1copy-affiliate .col-md-7, .e1copy-affiliate .col-md-8, .e1copy-affiliate .col-md-9,
        .e1copy-affiliate .col-md-10, .e1copy-affiliate .col-md-11, .e1copy-affiliate .col-md-12,
        .e1copy-affiliate .col-sm-1, .e1copy-affiliate .col-sm-2, .e1copy-affiliate .col-sm-3,
        .e1copy-affiliate .col-sm-4, .e1copy-affiliate .col-sm-5, .e1copy-affiliate .col-sm-6,
        .e1copy-affiliate .col-sm-7, .e1copy-affiliate .col-sm-8, .e1copy-affiliate .col-sm-9,
        .e1copy-affiliate .col-sm-10, .e1copy-affiliate .col-sm-11, .e1copy-affiliate .col-sm-12 {
            position: relative;
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
        }

        /* Garantir que os botões do Bootstrap tenham o estilo correto */
        .e1copy-affiliate .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
    ');
}

// Registrar os assets no frontend
add_action('wp_enqueue_scripts', 'e1copy_ai_register_assets');

/**
 * Carrega os recursos específicos para a página de afiliados
 */
function e1copy_ai_load_affiliates_assets() {
    // Verificar se estamos na página de afiliados
    $screen = get_current_screen();
    if (!$screen) return;

    // Verificar se estamos na página "Meus Afiliados"
    if ($screen->id !== 'e1copy-ai_page_e1copy-ai-affiliates') return;

    // Carregar os recursos específicos para a página de afiliados
    wp_enqueue_style('e1copy-bootstrap');
    wp_enqueue_style('e1copy-font-awesome');
    wp_enqueue_style('e1copy-bootstrap-icons');
    wp_enqueue_style('e1copy-affiliate-custom-css');

    // Carregar os scripts necessários
    wp_enqueue_script('jquery');
    wp_enqueue_script('e1copy-popper-js');
    wp_enqueue_script('e1copy-bootstrap-js');
}

// Carregar os recursos na área administrativa
add_action('admin_enqueue_scripts', 'e1copy_ai_load_affiliates_assets');

/**
 * Adiciona suporte para o Elementor
 */
function e1copy_ai_elementor_assets_support() {
    // Verificar se o Elementor está ativo
    if (did_action('elementor/loaded')) {
        // Adicionar suporte para o editor do Elementor
        add_action('elementor/preview/enqueue_styles', 'e1copy_ai_register_assets');

        // Adicionar suporte para o frontend do Elementor
        add_action('elementor/frontend/after_enqueue_styles', 'e1copy_ai_register_assets');
    }
}
add_action('init', 'e1copy_ai_elementor_assets_support');
