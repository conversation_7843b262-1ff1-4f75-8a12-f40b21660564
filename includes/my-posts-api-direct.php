<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Registra os endpoints da API REST para os posts
 */
function e1copy_register_api_endpoints() {
    // Namespace da API
    $namespace = 'e1copy-ai/v1';

    // Rota para testar a API (não requer autenticação)
    register_rest_route($namespace, '/test', [
        'methods' => 'GET',
        'callback' => 'e1copy_test_api',
        'permission_callback' => '__return_true',
    ]);

    // Rota para verificar a estrutura da tabela (temporária para depuração)
    register_rest_route($namespace, '/debug/table-structure', [
        'methods' => 'GET',
        'callback' => 'e1copy_debug_table_structure',
        'permission_callback' => '__return_true',
    ]);

    // Rota para corrigir a estrutura da tabela (temporária para depuração)
    register_rest_route($namespace, '/debug/fix-table', [
        'methods' => 'GET',
        'callback' => 'e1copy_debug_fix_table',
        'permission_callback' => '__return_true',
    ]);

    // Rota para verificar a chave de API
    register_rest_route($namespace, '/verify-key', [
        'methods' => 'GET',
        'callback' => 'e1copy_verify_api_key',
        'permission_callback' => '__return_true',
    ]);

    // Rota para listar todos os posts
    register_rest_route($namespace, '/posts', [
        'methods' => 'GET',
        'callback' => 'e1copy_get_posts',
        'permission_callback' => 'e1copy_check_api_key',
    ]);

    // Rota para listar posts de produto
    register_rest_route($namespace, '/products', [
        'methods' => 'GET',
        'callback' => 'e1copy_get_product_posts',
        'permission_callback' => 'e1copy_check_api_key',
    ]);

    // Rota para obter um post específico
    register_rest_route($namespace, '/posts/(?P<id>[\d]+)', [
        'methods' => 'GET',
        'callback' => 'e1copy_get_post',
        'permission_callback' => 'e1copy_check_api_key',
    ]);

    // Rota para remover um post
    register_rest_route($namespace, '/posts/(?P<id>[\d]+)', [
        'methods' => 'DELETE',
        'callback' => 'e1copy_delete_post',
        'permission_callback' => 'e1copy_check_api_key',
    ]);

    // Rota para marcar um post como processado ou não processado
    register_rest_route($namespace, '/posts/(?P<id>[\d]+)/mark-processed', [
        'methods' => 'POST',
        'callback' => 'e1copy_mark_post_processed',
        'permission_callback' => 'e1copy_check_api_key',
        'args' => [
            'processed' => [
                'default' => true,
                'sanitize_callback' => function($param) {
                    return filter_var($param, FILTER_VALIDATE_BOOLEAN);
                }
            ]
        ],
    ]);
}
add_action('rest_api_init', 'e1copy_register_api_endpoints');

/**
 * Adiciona cabeçalhos CORS e JSON para todas as respostas da API
 */
function e1copy_add_cors_headers() {
    // Permitir acesso de qualquer origem
    header('Access-Control-Allow-Origin: *');

    // Métodos HTTP permitidos
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE, PATCH');

    // Cabeçalhos permitidos (expandidos para incluir mais variações)
    header('Access-Control-Allow-Headers: X-E1Copy-API-Key, x-e1copy-api-key, Content-Type, Authorization, Origin, Accept, X-Requested-With');

    // Permitir credenciais
    header('Access-Control-Allow-Credentials: true');

    // Tempo de cache para preflight
    header('Access-Control-Max-Age: 3600');

    // Tipo de conteúdo
    header('Content-Type: application/json; charset=utf-8');

    // Não vamos mais usar exit para OPTIONS requests, pois o WordPress REST API já lida com isso
    // Apenas definimos o status 200 para preflight
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        status_header(200);
    }
}

/**
 * Verifica se a requisição está autenticada via chave de API
 */
function e1copy_check_api_key($request) {
    e1copy_add_cors_headers();

    // Registrar todos os cabeçalhos recebidos para depuração
    $headers = $request->get_headers();
    $headers_str = '';
    foreach ($headers as $key => $value) {
        $headers_str .= "$key: " . (is_array($value) ? implode(', ', $value) : $value) . "\n";
    }
    error_log('E1Copy API: Cabeçalhos recebidos: ' . $headers_str);

    // Verificar se o usuário tem uma chave de API válida
    $api_key = $request->get_header('X-E1Copy-API-Key');

    // Tentar outros formatos de cabeçalho
    if (!$api_key) {
        $api_key = $request->get_header('x-e1copy-api-key');
    }

    // Verificar no parâmetro da URL
    if (!$api_key && isset($request['api_key'])) {
        $api_key = $request['api_key'];
        error_log('E1Copy API: Usando api_key da URL: ' . $api_key);
    }

    // Verificar no Authorization header
    if (!$api_key) {
        $auth_header = $request->get_header('Authorization');
        if ($auth_header && strpos($auth_header, 'Bearer ') === 0) {
            $api_key = substr($auth_header, 7);
            error_log('E1Copy API: Usando Bearer token como API key: ' . $api_key);
        }
    }

    // Verificar em outros cabeçalhos comuns
    $other_headers = ['Api-Key', 'Apikey', 'Api_Key', 'Key', 'Token'];
    foreach ($other_headers as $header) {
        if (!$api_key && $request->get_header($header)) {
            $api_key = $request->get_header($header);
            error_log('E1Copy API: Usando cabeçalho alternativo ' . $header . ': ' . $api_key);
            break;
        }
    }

    if (!$api_key) {
        error_log('E1Copy API: Nenhuma chave de API encontrada na requisição');
        return false;
    }

    $valid_key = get_option('e1copy_ai_rest_api_key');
    if (!$valid_key) {
        error_log('E1Copy API: Nenhuma chave de API configurada no WordPress');
        return false;
    }

    // Comparar as chaves (ignorando espaços em branco)
    $api_key = trim($api_key);
    $valid_key = trim($valid_key);

    if ($api_key !== $valid_key) {
        error_log('E1Copy API: Chave inválida fornecida: ' . substr($api_key, 0, 5) . '... (esperada: ' . substr($valid_key, 0, 5) . '...)');
        return false;
    }

    error_log('E1Copy API: Autenticação bem-sucedida com a chave de API: ' . substr($api_key, 0, 5) . '...');

    // Definir o usuário atual como administrador para garantir permissões adequadas
    $admin_users = get_users(['role' => 'administrator', 'number' => 1]);
    if (!empty($admin_users)) {
        wp_set_current_user($admin_users[0]->ID);
        error_log('E1Copy API: Usuário administrador definido: ID ' . $admin_users[0]->ID . ', Login: ' . $admin_users[0]->user_login);
    }

    return true;
}

/**
 * Endpoint de teste para verificar se a API está funcionando
 */
function e1copy_test_api() {
    e1copy_add_cors_headers();

    $response = [
        'success' => true,
        'message' => 'API E1Copy está funcionando corretamente!',
        'time' => current_time('mysql'),
        'version' => '1.0'
    ];

    return rest_ensure_response($response);
}

/**
 * Endpoint para verificar se a chave de API é válida
 */
function e1copy_verify_api_key($request) {
    e1copy_add_cors_headers();

    // Registrar todos os cabeçalhos recebidos para depuração
    $headers = $request->get_headers();
    $headers_str = '';
    foreach ($headers as $key => $value) {
        $headers_str .= "$key: " . (is_array($value) ? implode(', ', $value) : $value) . "\n";
    }
    error_log('E1Copy API Verify: Cabeçalhos recebidos: ' . $headers_str);

    // Verificar a chave de API em vários lugares
    $api_key = null;
    $source = 'não encontrada';

    // Verificar em vários lugares
    if ($request->get_header('X-E1Copy-API-Key')) {
        $api_key = $request->get_header('X-E1Copy-API-Key');
        $source = 'cabeçalho X-E1Copy-API-Key';
    } elseif ($request->get_header('x-e1copy-api-key')) {
        $api_key = $request->get_header('x-e1copy-api-key');
        $source = 'cabeçalho x-e1copy-api-key (minúsculo)';
    } elseif (isset($request['api_key'])) {
        $api_key = $request['api_key'];
        $source = 'parâmetro api_key na URL';
    } elseif ($request->get_header('Authorization') && strpos($request->get_header('Authorization'), 'Bearer ') === 0) {
        $api_key = substr($request->get_header('Authorization'), 7);
        $source = 'cabeçalho Authorization (Bearer)';
    }

    // Verificar em outros cabeçalhos comuns
    $other_headers = ['Api-Key', 'Apikey', 'Api_Key', 'Key', 'Token'];
    foreach ($other_headers as $header) {
        if (!$api_key && $request->get_header($header)) {
            $api_key = $request->get_header($header);
            $source = 'cabeçalho ' . $header;
            break;
        }
    }

    // Verificar se a chave foi fornecida
    if (!$api_key) {
        $response = [
            'success' => false,
            'message' => 'Nenhuma chave de API fornecida',
            'help' => 'Adicione o cabeçalho X-E1Copy-API-Key com sua chave de API ou use ?api_key= na URL',
            'headers_received' => array_keys($headers)
        ];
        return rest_ensure_response($response);
    }

    // Verificar se a chave é válida
    $valid_key = get_option('e1copy_ai_rest_api_key');
    if (!$valid_key) {
        $response = [
            'success' => false,
            'message' => 'Nenhuma chave de API configurada no WordPress',
            'help' => 'Configure uma chave de API nas configurações do plugin'
        ];
        return rest_ensure_response($response);
    }

    // Comparar as chaves (ignorando espaços em branco)
    $api_key = trim($api_key);
    $valid_key = trim($valid_key);

    if ($api_key !== $valid_key) {
        $response = [
            'success' => false,
            'message' => 'Chave de API inválida',
            'help' => 'Verifique se a chave está correta',
            'key_provided' => substr($api_key, 0, 5) . '...',
            'key_expected' => substr($valid_key, 0, 5) . '...',
            'source' => $source
        ];
        return rest_ensure_response($response);
    }

    // Se chegou aqui, a chave é válida
    $response = [
        'success' => true,
        'message' => 'Chave de API válida',
        'source' => $source,
        'key_prefix' => substr($api_key, 0, 5) . '...'
    ];

    return rest_ensure_response($response);
}

/**
 * Obtém a lista de todos os posts
 */
function e1copy_get_posts($request) {
    e1copy_add_cors_headers();

    // Verificar autenticação manualmente para fornecer mensagens de erro detalhadas
    $api_key = null;

    // Verificar em vários lugares
    if ($request->get_header('X-E1Copy-API-Key')) {
        $api_key = $request->get_header('X-E1Copy-API-Key');
    } elseif ($request->get_header('x-e1copy-api-key')) {
        $api_key = $request->get_header('x-e1copy-api-key');
    } elseif (isset($request['api_key'])) {
        $api_key = $request['api_key'];
    } elseif ($request->get_header('Authorization') && strpos($request->get_header('Authorization'), 'Bearer ') === 0) {
        $api_key = substr($request->get_header('Authorization'), 7);
    }

    // Verificar se a chave foi fornecida
    if (!$api_key) {
        $error_response = [
            'success' => false,
            'error' => 'API Key não fornecida',
            'message' => 'Você deve fornecer uma chave de API válida para acessar este endpoint',
            'help' => 'Adicione o cabeçalho X-E1Copy-API-Key com sua chave de API ou use ?api_key= na URL'
        ];
        return rest_ensure_response($error_response);
    }

    // Verificar se a chave é válida
    $valid_key = get_option('e1copy_ai_rest_api_key');
    if (!$valid_key || trim($api_key) !== trim($valid_key)) {
        $error_response = [
            'success' => false,
            'error' => 'API Key inválida',
            'message' => 'A chave de API fornecida não é válida',
            'help' => 'Verifique se a chave está correta nas configurações do plugin'
        ];
        return rest_ensure_response($error_response);
    }

    // Se chegou aqui, a autenticação foi bem-sucedida
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    try {
        // Parâmetros de paginação
        $per_page = isset($request['per_page']) ? (int) $request['per_page'] : 10;
        $page = isset($request['page']) ? (int) $request['page'] : 1;
        $offset = ($page - 1) * $per_page;

        // Parâmetros de filtro
        $status = isset($request['status']) ? sanitize_text_field($request['status']) : '';
        $processed = isset($request['processed']) ? filter_var($request['processed'], FILTER_VALIDATE_BOOLEAN) : null;
        $post_type = isset($request['post_type']) ? sanitize_text_field($request['post_type']) : 'blog';

        // Construir a consulta
        $where = [];
        $values = [];

        // Filtrar por tipo de post (padrão: blog)
        $where[] = 'post_type = %s';
        $values[] = $post_type;
        error_log('E1Copy API Direct: Filtrando posts do tipo ' . $post_type);

        if ($status) {
            $where[] = 'status = %s';
            $values[] = $status;
        }

        // Verificar se o parâmetro processed foi fornecido
        if ($processed !== null) {
            $where[] = 'processed = %d';
            $values[] = $processed ? 1 : 0;
            error_log('E1Copy API Direct: Filtrando posts com processed = ' . ($processed ? '1 (processados)' : '0 (não processados)'));
        } else {
            // Por padrão, se nenhum valor for especificado, mostrar posts não processados
            $where[] = 'processed = %d';
            $values[] = 0;
            error_log('E1Copy API Direct: Nenhum valor de processed fornecido, mostrando posts não processados (processed = 0)');
        }

        // Log detalhado para depuração
        error_log('E1Copy API Direct: Consulta WHERE: ' . implode(' AND ', $where));
        error_log('E1Copy API Direct: Valores: ' . print_r($values, true));

        // Verificar se há posts com processed = 0 para depuração
        $count_unprocessed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed = 0");
        error_log('E1Copy API Direct: Total de posts com processed = 0 (não processados): ' . $count_unprocessed);

        // Verificar se há posts com processed = 1 para depuração
        $count_processed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed = 1");
        error_log('E1Copy API Direct: Total de posts com processed = 1 (processados): ' . $count_processed);

        $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        // Verificar se a tabela existe
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        if (!$table_exists) {
            throw new Exception("A tabela $table_name não existe no banco de dados");
        }

        // Verificar se a coluna processed existe
        $column_exists = false;
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        foreach ($columns as $column) {
            if ($column->Field === 'processed') {
                $column_exists = true;
                break;
            }
        }

        if (!$column_exists && $processed !== null) {
            throw new Exception("A coluna 'processed' não existe na tabela $table_name");
        }

        // Obter posts
        $query = $wpdb->prepare(
            "SELECT * FROM $table_name $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d",
            array_merge($values, [$per_page, $offset])
        );

        // Log da consulta SQL para depuração
        error_log('E1Copy API Direct: Consulta SQL: ' . $query);

        $posts = $wpdb->get_results($query, ARRAY_A) ?: [];

        // Log do número de posts encontrados
        error_log('E1Copy API Direct: Número de posts encontrados: ' . count($posts));

        // Processar dados para cada post
        foreach ($posts as &$post) {
            // Deserializar categorias e converter para string simples
            if (isset($post['categories'])) {
                $categories = maybe_unserialize($post['categories']);
                // Se for um array, pegar apenas o primeiro ID como string
                if (is_array($categories) && !empty($categories)) {
                    $post['categories'] = (string) $categories[0];
                } elseif (!empty($categories)) {
                    $post['categories'] = (string) $categories;
                } else {
                    $post['categories'] = '';
                }
            }

            // Processar tags para exibição
            if (!empty($post['tags'])) {
                $post['tag_names'] = array_map('trim', explode(',', $post['tags']));
            } else {
                $post['tag_names'] = [];
            }

            // Processar imagens do produto
            if (isset($post['product_images']) && !empty($post['product_images'])) {
                try {
                    $images = json_decode($post['product_images'], true);
                    if (is_array($images)) {
                        $post['product_images_array'] = $images;
                    } else {
                        $post['product_images_array'] = [];
                    }
                } catch (Exception $e) {
                    $post['product_images_array'] = [];
                    error_log('E1Copy API Direct: Erro ao decodificar product_images para o post ID ' . $post['id'] . ': ' . $e->getMessage());
                }
            } else {
                $post['product_images_array'] = [];
            }

            // Garantir que o ID da imagem de capa seja um inteiro
            if (isset($post['post_cover_image_id'])) {
                $post['post_cover_image_id'] = intval($post['post_cover_image_id']);
            } else {
                $post['post_cover_image_id'] = 0;
            }

            // Processar informações do afiliado
            if (isset($post['affiliate_id']) && intval($post['affiliate_id']) > 0) {
                $affiliate_id = intval($post['affiliate_id']);
                $affiliate = e1copy_ai_get_affiliate($affiliate_id);

                if ($affiliate) {
                    $post['affiliate'] = [
                        'id' => $affiliate_id,
                        'title' => $affiliate['title'],
                        'content' => $affiliate['content'],
                        'affiliate_link' => isset($affiliate['affiliate_link']) ? $affiliate['affiliate_link'] : '',
                        'shortcode' => '[e1copy_affiliate id="' . $affiliate_id . '"]',
                        'link_shortcode' => '[e1copy_affiliate id="' . $affiliate_id . '" show_link="yes"]'
                    ];
                } else {
                    $post['affiliate'] = null;
                }
            } else {
                $post['affiliate'] = null;
            }
        }

        // Contar total de posts
        $count_query = "SELECT COUNT(*) FROM $table_name $where_clause";
        if (!empty($values)) {
            $count_query = $wpdb->prepare($count_query, $values);
        }
        $total_posts = (int) $wpdb->get_var($count_query);

        // Preparar resposta
        $response = [
            'success' => true,
            'posts' => $posts,
            'total' => $total_posts,
            'pages' => ceil($total_posts / $per_page),
            'page' => $page,
            'per_page' => $per_page,
        ];

        return rest_ensure_response($response);
    } catch (Exception $e) {
        $error_response = [
            'success' => false,
            'error' => 'Erro ao processar a requisição',
            'message' => $e->getMessage(),
            'code' => $e->getCode()
        ];
        return rest_ensure_response($error_response);
    }
}

/**
 * Obtém um post específico pelo ID
 */
function e1copy_get_post($request) {
    e1copy_add_cors_headers();

    $id = (int) $request['id'];
    $post = e1copy_ai_get_my_post($id);

    if (!$post) {
        $response = [
            'success' => false,
            'error' => 'Post não encontrado',
            'code' => 'post_not_found'
        ];
        return rest_ensure_response($response);
    }

    // Converter categorias para string simples e adicionar informações de categorias
    if (!empty($post['categories'])) {
        $categories = $post['categories'];
        // Se for um array, pegar apenas o primeiro ID como string
        if (is_array($categories) && !empty($categories)) {
            $post['categories'] = (string) $categories[0];
            $first_cat_id = $categories[0];
        } elseif (!empty($categories)) {
            $post['categories'] = (string) $categories;
            $first_cat_id = $categories;
        } else {
            $post['categories'] = '';
            $first_cat_id = null;
        }

        // Adicionar nomes das categorias
        $post['category_names'] = [];
        if ($first_cat_id) {
            $category = get_category($first_cat_id);
            if ($category) {
                $post['category_names'][] = $category->name;
            }
        }
    }

    // Processar tags para exibição
    if (!empty($post['tags'])) {
        $post['tag_names'] = array_map('trim', explode(',', $post['tags']));
    } else {
        $post['tag_names'] = [];
    }

    // Processar imagens do produto
    if (isset($post['product_images']) && !empty($post['product_images'])) {
        try {
            $images = json_decode($post['product_images'], true);
            if (is_array($images)) {
                $post['product_images_array'] = $images;
            } else {
                $post['product_images_array'] = [];
            }
        } catch (Exception $e) {
            $post['product_images_array'] = [];
            error_log('E1Copy API Direct: Erro ao decodificar product_images para o post ID ' . $post['id'] . ': ' . $e->getMessage());
        }
    } else {
        $post['product_images_array'] = [];
    }

    // Garantir que o ID da imagem de capa seja um inteiro
    if (isset($post['post_cover_image_id'])) {
        $post['post_cover_image_id'] = intval($post['post_cover_image_id']);
        // Log para depuração
        error_log('E1Copy API Direct: Post ID ' . $post['id'] . ' tem imagem de capa com ID ' . $post['post_cover_image_id']);
    } else {
        $post['post_cover_image_id'] = 0;
    }

    // Processar informações do afiliado
    if (isset($post['affiliate_id']) && intval($post['affiliate_id']) > 0) {
        $affiliate_id = intval($post['affiliate_id']);
        $affiliate = e1copy_ai_get_affiliate($affiliate_id);

        if ($affiliate) {
            $post['affiliate'] = [
                'id' => $affiliate_id,
                'title' => $affiliate['title'],
                'content' => $affiliate['content'],
                'affiliate_link' => isset($affiliate['affiliate_link']) ? $affiliate['affiliate_link'] : '',
                'shortcode' => '[e1copy_affiliate id="' . $affiliate_id . '"]',
                'link_shortcode' => '[e1copy_affiliate id="' . $affiliate_id . '" show_link="yes"]'
            ];
        } else {
            $post['affiliate'] = null;
        }
    } else {
        $post['affiliate'] = null;
    }

    $response = [
        'success' => true,
        'post' => $post
    ];

    return rest_ensure_response($response);
}

/**
 * Função de depuração para corrigir a estrutura da tabela
 */
function e1copy_debug_fix_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    // Verificar se a tabela existe
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    if (!$table_exists) {
        return rest_ensure_response([
            'success' => false,
            'message' => "A tabela $table_name não existe."
        ]);
    }

    // Verificar se a coluna 'processed' existe
    $column_exists = false;
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    foreach ($columns as $column) {
        if ($column->Field === 'processed') {
            $column_exists = true;
            break;
        }
    }

    $actions = [];

    // Se a coluna não existir, adicioná-la
    if (!$column_exists) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN processed tinyint(1) DEFAULT 0");
        $actions[] = "Coluna 'processed' adicionada à tabela.";
    } else {
        // Verificar o tipo da coluna
        foreach ($columns as $column) {
            if ($column->Field === 'processed') {
                if ($column->Type !== 'tinyint(1)') {
                    $wpdb->query("ALTER TABLE $table_name MODIFY COLUMN processed tinyint(1) DEFAULT 0");
                    $actions[] = "Tipo da coluna 'processed' corrigido para tinyint(1).";
                }
                break;
            }
        }
    }

    // Verificar se há posts com valor NULL na coluna processed
    $null_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed IS NULL");
    if ($null_count > 0) {
        $wpdb->query("UPDATE $table_name SET processed = 0 WHERE processed IS NULL");
        $actions[] = "Corrigidos $null_count registros com valor NULL na coluna 'processed'.";
    }

    // Verificar a estrutura atualizada
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $processed_column = null;
    foreach ($columns as $column) {
        if ($column->Field === 'processed') {
            $processed_column = $column;
            break;
        }
    }

    // Obter alguns dados da tabela
    $posts = $wpdb->get_results("SELECT id, title, processed FROM $table_name ORDER BY id DESC LIMIT 10", ARRAY_A);

    // Preparar resposta
    $response = [
        'success' => true,
        'actions' => $actions,
        'table_name' => $table_name,
        'processed_column' => $processed_column,
        'sample_data' => $posts
    ];

    return rest_ensure_response($response);
}

/**
 * Função de depuração para verificar a estrutura da tabela
 */
function e1copy_debug_table_structure() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    // Verificar se a tabela existe
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
    if (!$table_exists) {
        return rest_ensure_response([
            'success' => false,
            'message' => "A tabela $table_name não existe."
        ]);
    }

    // Obter a estrutura da tabela
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");

    // Verificar especificamente a coluna 'processed'
    $processed_column = null;
    foreach ($columns as $column) {
        if ($column->Field === 'processed') {
            $processed_column = $column;
            break;
        }
    }

    // Obter alguns dados da tabela
    $posts = $wpdb->get_results("SELECT id, title, processed FROM $table_name ORDER BY id DESC LIMIT 10", ARRAY_A);

    // Preparar resposta
    $response = [
        'success' => true,
        'table_name' => $table_name,
        'columns' => $columns,
        'processed_column' => $processed_column,
        'sample_data' => $posts
    ];

    return rest_ensure_response($response);
}

/**
 * Obtém a lista de posts de produto
 */
function e1copy_get_product_posts($request) {
    // Forçar o tipo de post como 'product'
    $request['post_type'] = 'product';
    return e1copy_get_posts($request);
}

/**
 * Remove um post
 */
function e1copy_delete_post($request) {
    e1copy_add_cors_headers();

    $id = (int) $request['id'];
    $post = e1copy_ai_get_my_post($id);

    if (!$post) {
        $response = [
            'success' => false,
            'error' => 'Post não encontrado',
            'code' => 'post_not_found'
        ];
        return rest_ensure_response($response);
    }

    // Remover o post
    $result = e1copy_ai_delete_my_post($id);

    // Log para depuração
    error_log('E1Copy API Direct Delete: Removendo post ID ' . $id);

    if (!$result) {
        $response = [
            'success' => false,
            'error' => 'Falha ao remover o post',
            'code' => 'delete_failed'
        ];
        return rest_ensure_response($response);
    }

    $response = [
        'success' => true,
        'message' => 'Post removido com sucesso',
        'id' => $id
    ];

    return rest_ensure_response($response);
}

/**
 * Marca um post como processado
 */
function e1copy_mark_post_processed($request) {
    e1copy_add_cors_headers();

    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    $id = (int) $request['id'];
    $post = e1copy_ai_get_my_post($id);

    if (!$post) {
        $response = [
            'success' => false,
            'error' => 'Post não encontrado',
            'code' => 'post_not_found'
        ];
        return rest_ensure_response($response);
    }

    // Log do estado atual do post
    error_log('E1Copy API Direct Mark: Estado atual do post ID ' . $id . ': processed = ' . $post['processed']);

    // Sempre marcamos como processado (valor 1 no banco)
    $db_value = 1;
    $result = $wpdb->update(
        $table_name,
        ['processed' => $db_value],
        ['id' => $id]
    );

    // Log para depuração
    error_log('E1Copy API Direct Mark: Marcando post ID ' . $id . ' como processado (processed = 1 no banco)');

    // Log detalhado para depuração
    error_log('E1Copy API Direct Mark: Query SQL: ' . $wpdb->last_query);
    error_log('E1Copy API Direct Mark: Resultado da atualização: ' . ($result !== false ? 'Sucesso' : 'Falha'));

    // Verificar se o post foi atualizado corretamente
    $updated_post = e1copy_ai_get_my_post($id);
    error_log('E1Copy API Direct Mark: Estado após atualização do post ID ' . $id . ': processed = ' . $updated_post['processed']);

    if ($result === false) {
        $response = [
            'success' => false,
            'error' => 'Falha ao atualizar o post',
            'code' => 'update_failed'
        ];
        return rest_ensure_response($response);
    }

    $response = [
        'success' => true,
        'message' => 'Post marcado como processado',
        'id' => $id
    ];

    return rest_ensure_response($response);
}
